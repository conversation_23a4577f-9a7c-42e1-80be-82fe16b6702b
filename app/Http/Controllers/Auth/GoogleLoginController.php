<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Rules\IndianMobileNumber;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash; // Or Str::random() for password
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class GoogleLoginController extends Controller
{
    public function redirectToGoogle()
    {
        try {
            return Socialite::driver('google')
                ->redirectUrl(url('/auth/callback/google'))
                ->scopes([
                    'openid',
                    'email',
                    'profile',
                    'https://www.googleapis.com/auth/user.birthday.read',
                    'https://www.googleapis.com/auth/user.gender.read',
                    'https://www.googleapis.com/auth/user.phonenumbers.read',
                    'https://www.googleapis.com/auth/contacts.readonly'
                ])
                ->redirect();
        } catch (\Exception $e) {
            return redirect('/login')->with('error', 'Unable to connect to Google. Please try again.');
        }
    }

    public function handleGoogleCallback()
    {
        try {
            \Log::info('Google OAuth callback received');

            $googleUser = Socialite::driver('google')
                ->redirectUrl(url('/auth/callback/google'))
                ->scopes([
                    'openid',
                    'email',
                    'profile',
                    'https://www.googleapis.com/auth/user.birthday.read',
                    'https://www.googleapis.com/auth/user.gender.read',
                    'https://www.googleapis.com/auth/user.phonenumbers.read',
                    'https://www.googleapis.com/auth/contacts.readonly'
                ])
                ->user();

            \Log::info('Google user data received: ' . $googleUser->getEmail());

            // Fetch additional data from People API
            $additionalData = null;
            try {
                $additionalData = $this->fetchAdditionalGoogleData($googleUser->token);
                \Log::info('Additional Google data fetched successfully');
            } catch (\Exception $e) {
                \Log::warning('Failed to fetch additional Google data: ' . $e->getMessage());
            }

            // Fetch Google contacts for debugging
            $contacts = null;
            try {
                $contacts = $this->fetchGoogleContacts($googleUser->token);
                \Log::info('Google contacts fetched successfully. Count: ' . count($contacts['connections'] ?? []));

                // Debug: Display contacts and exit
                echo "<h2>Google Contacts Debug Information</h2>";
                echo "<h3>Total Contacts: " . count($contacts['connections'] ?? []) . "</h3>";
                echo "<pre>";
                print_r($contacts);
                echo "</pre>";
                exit();

            } catch (\Exception $e) {
                \Log::warning('Failed to fetch Google contacts: ' . $e->getMessage());
                echo "<h2>Error fetching contacts:</h2>";
                echo "<pre>" . $e->getMessage() . "</pre>";
                exit();
            }

            // Check if user already exists
            $existingUser = User::where('google_id', $googleUser->getId())->first();
            $isNewUser = !$existingUser;

            // Prepare user data with Google information
            $userData = [
                'name' => $googleUser->getName(),
                'email' => $googleUser->getEmail(),
                'password' => Hash::make(uniqid()), // Password is required but not used
            ];

            // Extract additional data if available
            if ($additionalData) {
                $extractedData = $this->extractGoogleProfileData($additionalData);
                $userData = array_merge($userData, $extractedData);
            }

            // Handle profile picture for new users or existing users without profile picture
            $shouldDownloadPicture = $isNewUser || ($existingUser && !$existingUser->profile_picture);

            if ($shouldDownloadPicture) {
                $profilePicturePath = $this->downloadAndStoreProfilePicture($googleUser->getAvatar(), $googleUser->getId());
                if ($profilePicturePath) {
                    $userData['profile_picture'] = $profilePicturePath;

                    // If updating existing user, delete old Google profile picture if it exists
                    if ($existingUser && $existingUser->profile_picture && str_contains($existingUser->profile_picture, 'google_')) {
                        Storage::disk('public')->delete($existingUser->profile_picture);
                        \Log::info('Deleted old Google profile picture: ' . $existingUser->profile_picture);
                    }
                }
            }

            // Find user by google_id or create new one
            $user = User::updateOrCreate([
                'google_id' => $googleUser->getId(),
            ], $userData);

            // For new users only, enable Time Spending Service by default
            if ($isNewUser) {
                $user->update([
                    'is_time_spending_enabled' => true
                ]);
                \Log::info('Time Spending Service enabled by default for new user: ' . $user->email);
            }

            Auth::login($user, true); // Log the user in

            \Log::info('User logged in successfully: ' . $user->email);

            // Redirect to home page - the root route will handle profile completion check
            return redirect('/')->with('success', 'Welcome back! You have successfully logged in.');

        } catch (\Exception $e) {
            echo "<h1>Google OAuth Error</h1>";
            echo "<pre>";
            echo "Error Message: " . $e->getMessage() . "\n";
            echo "Error File: " . $e->getFile() . "\n";
            echo "Error Line: " . $e->getLine() . "\n";
            echo "Stack Trace:\n" . $e->getTraceAsString();
            echo "</pre>";
            exit();
        }
    }

    /**
     * Fetch additional user data from Google People API
     */
    private function fetchAdditionalGoogleData($accessToken)
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $accessToken,
            'Accept' => 'application/json',
        ])->get('https://people.googleapis.com/v1/people/me', [
            'personFields' => 'birthdays,genders,phoneNumbers,emailAddresses,names,photos'
        ]);

        if ($response->successful()) {
            return $response->json();
        } else {
            throw new \Exception('Failed to fetch additional data: ' . $response->body());
        }
    }

    /**
     * Fetch Google contacts from People API
     */
    private function fetchGoogleContacts($accessToken)
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $accessToken,
            'Accept' => 'application/json',
        ])->get('https://people.googleapis.com/v1/people/me/connections', [
            'personFields' => 'names,emailAddresses,phoneNumbers,photos',
            'pageSize' => 1000  // Maximum allowed
        ]);

        if ($response->successful()) {
            return $response->json();
        } else {
            throw new \Exception('Failed to fetch contacts: ' . $response->body());
        }
    }

    /**
     * Extract and process Google profile data
     */
    private function extractGoogleProfileData($googleData)
    {
        $extractedData = [];

        // Extract date of birth
        if (isset($googleData['birthdays']) && !empty($googleData['birthdays'])) {
            foreach ($googleData['birthdays'] as $birthday) {
                if (isset($birthday['date']) && isset($birthday['date']['year'])) {
                    $date = $birthday['date'];
                    if (isset($date['year']) && isset($date['month']) && isset($date['day'])) {
                        $extractedData['date_of_birth'] = sprintf(
                            '%04d-%02d-%02d',
                            $date['year'],
                            $date['month'],
                            $date['day']
                        );
                        break; // Use the first complete birthday found
                    }
                }
            }
        }

        // Extract gender
        if (isset($googleData['genders']) && !empty($googleData['genders'])) {
            foreach ($googleData['genders'] as $gender) {
                if (isset($gender['value'])) {
                    $genderValue = strtolower($gender['value']);
                    // Map Google gender values to our system
                    if (in_array($genderValue, ['male', 'female'])) {
                        $extractedData['gender'] = $genderValue;
                        break;
                    } elseif ($genderValue === 'other') {
                        $extractedData['gender'] = 'other';
                        break;
                    }
                }
            }
        }

        // Extract phone number (validate for 10 digits)
        if (isset($googleData['phoneNumbers']) && !empty($googleData['phoneNumbers'])) {
            foreach ($googleData['phoneNumbers'] as $phone) {
                if (isset($phone['value'])) {
                    $phoneNumber = $this->validateAndFormatPhoneNumber($phone['value']);
                    if ($phoneNumber) {
                        $extractedData['contact_number'] = $phoneNumber;
                        break; // Use the first valid phone number found
                    }
                }
            }
        }

        return $extractedData;
    }

    /**
     * Validate and format phone number to 10 digits
     */
    private function validateAndFormatPhoneNumber($phoneNumber)
    {
        return IndianMobileNumber::formatPhoneNumber($phoneNumber);
    }

    /**
     * Download and store Google profile picture locally
     */
    private function downloadAndStoreProfilePicture($avatarUrl, $googleId)
    {
        if (!$avatarUrl || empty(trim($avatarUrl))) {
            \Log::info('No Google avatar URL provided for user: ' . $googleId);
            return null;
        }

        try {
            // Get a higher resolution version of the avatar
            // Try different resolution options
            $highResUrl = str_replace(['=s96-c', '=s96', '=c96'], '=s400-c', $avatarUrl);

            // If no size parameter found, add one
            if (!str_contains($highResUrl, '=s')) {
                $highResUrl = $avatarUrl . '=s400-c';
            }

            \Log::info('Downloading Google profile picture from: ' . $highResUrl);

            // Download the image with proper headers
            $response = Http::timeout(30)
                ->withHeaders([
                    'User-Agent' => 'Mozilla/5.0 (compatible; DatingApp/1.0)',
                ])
                ->get($highResUrl);

            if (!$response->successful()) {
                \Log::warning('Failed to download Google profile picture. Status: ' . $response->status() . ', URL: ' . $highResUrl);
                return null;
            }

            // Get the image content
            $imageContent = $response->body();

            // Validate that we actually got image content
            if (empty($imageContent) || strlen($imageContent) < 100) {
                \Log::warning('Downloaded image content is too small or empty');
                return null;
            }

            // Determine file extension from content type
            $contentType = $response->header('Content-Type', '');
            $extension = 'jpg'; // default

            if (str_contains($contentType, 'png')) {
                $extension = 'png';
            } elseif (str_contains($contentType, 'gif')) {
                $extension = 'gif';
            } elseif (str_contains($contentType, 'webp')) {
                $extension = 'webp';
            } elseif (str_contains($contentType, 'jpeg')) {
                $extension = 'jpg';
            }

            // Generate unique filename
            $filename = 'google_' . $googleId . '_' . time() . '_' . Str::random(8) . '.' . $extension;
            $path = 'profile-pictures/' . $filename;

            // Ensure the directory exists
            if (!Storage::disk('public')->exists('profile-pictures')) {
                Storage::disk('public')->makeDirectory('profile-pictures');
            }

            // Store the image
            $stored = Storage::disk('public')->put($path, $imageContent);

            if ($stored) {
                \Log::info('Google profile picture stored successfully: ' . $path . ' (Size: ' . strlen($imageContent) . ' bytes)');
                return $path;
            } else {
                \Log::error('Failed to store Google profile picture to: ' . $path);
                return null;
            }

        } catch (\Exception $e) {
            \Log::error('Error downloading Google profile picture: ' . $e->getMessage() . ' | URL: ' . ($avatarUrl ?? 'null'));
            return null;
        }
    }
}
