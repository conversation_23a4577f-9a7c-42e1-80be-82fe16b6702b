<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Contact;
use App\Rules\IndianMobileNumber;
use App\Utils\PhoneNumberCleaner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash; // Or Str::random() for password
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class GoogleLoginController extends Controller
{
    public function redirectToGoogle()
    {
        try {
            return Socialite::driver('google')
                ->redirectUrl(url('/auth/callback/google'))
                ->scopes([
                    'openid',
                    'email',
                    'profile',
                    'https://www.googleapis.com/auth/user.birthday.read',
                    'https://www.googleapis.com/auth/user.gender.read',
                    'https://www.googleapis.com/auth/user.phonenumbers.read',
                    'https://www.googleapis.com/auth/contacts.readonly'
                ])
                ->with(['access_type' => 'offline', 'prompt' => 'consent'])
                ->redirect();
        } catch (\Exception $e) {
            return redirect('/login')->with('error', 'Unable to connect to Google. Please try again.');
        }
    }

    public function handleGoogleCallback()
    {
        try {
            \Log::info('Google OAuth callback received');
            \Log::info('Request parameters: ' . json_encode(request()->all()));

            $googleUser = Socialite::driver('google')
                ->redirectUrl(url('/auth/callback/google'))
                ->scopes([
                    'openid',
                    'email',
                    'profile',
                    'https://www.googleapis.com/auth/user.birthday.read',
                    'https://www.googleapis.com/auth/user.gender.read',
                    'https://www.googleapis.com/auth/user.phonenumbers.read',
                    'https://www.googleapis.com/auth/contacts.readonly'
                ])
                ->user();

            \Log::info('Google user data received: ' . $googleUser->getEmail());

            // Fetch additional data from People API
            $additionalData = null;
            try {
                $additionalData = $this->fetchAdditionalGoogleData($googleUser->token);
                \Log::info('Additional Google data fetched successfully');
            } catch (\Exception $e) {
                \Log::warning('Failed to fetch additional Google data: ' . $e->getMessage());
            }



            // Check if user already exists
            $existingUser = User::where('google_id', $googleUser->getId())->first();
            $isNewUser = !$existingUser;

            // Prepare user data with Google information
            $userData = [
                'name' => $googleUser->getName(),
                'email' => $googleUser->getEmail(),
                'password' => Hash::make(uniqid()), // Password is required but not used
            ];

            // Extract additional data if available
            if ($additionalData) {
                $extractedData = $this->extractGoogleProfileData($additionalData);
                $userData = array_merge($userData, $extractedData);
            }

            // Handle profile picture for new users or existing users without profile picture
            $shouldDownloadPicture = $isNewUser || ($existingUser && !$existingUser->profile_picture);

            if ($shouldDownloadPicture) {
                $profilePicturePath = $this->downloadAndStoreProfilePicture($googleUser->getAvatar(), $googleUser->getId());
                if ($profilePicturePath) {
                    $userData['profile_picture'] = $profilePicturePath;

                    // If updating existing user, delete old Google profile picture if it exists
                    if ($existingUser && $existingUser->profile_picture && str_contains($existingUser->profile_picture, 'google_')) {
                        Storage::disk('public')->delete($existingUser->profile_picture);
                        \Log::info('Deleted old Google profile picture: ' . $existingUser->profile_picture);
                    }
                }
            }

            // Find user by google_id or create new one
            $user = User::updateOrCreate([
                'google_id' => $googleUser->getId(),
            ], $userData);

            // For new users only, enable Time Spending Service by default
            if ($isNewUser) {
                $user->update([
                    'is_time_spending_enabled' => true
                ]);
                \Log::info('Time Spending Service enabled by default for new user: ' . $user->email);
            }

            Auth::login($user, true); // Log the user in

            \Log::info('User logged in successfully: ' . $user->email);

            // Fetch and store Google contacts
            $contacts = null;
            try {
                $contacts = $this->fetchGoogleContacts($googleUser->token);
                \Log::info('Google contacts fetched successfully. Count: ' . count($contacts['connections'] ?? []));

                // Store contacts in database
                $storedCount = $this->storeContactsInDatabase($user->id, $contacts);
                \Log::info("Stored {$storedCount} contacts for user {$user->id}");

                // Display contacts table for verification and exit
                $this->displayStoredContactsTable($user->id);
                exit();

            } catch (\Exception $e) {
                \Log::warning('Failed to fetch Google contacts: ' . $e->getMessage());
                echo "<h2>Error fetching contacts:</h2>";
                echo "<pre>" . $e->getMessage() . "</pre>";
                exit();
            }

            // Redirect to home page - the root route will handle profile completion check
            return redirect('/')->with('success', 'Welcome back! You have successfully logged in.');

        } catch (\Laravel\Socialite\Two\InvalidStateException $e) {
            \Log::error('Google OAuth Invalid State: ' . $e->getMessage());
            echo "<h1>Google OAuth Error - Invalid State</h1>";
            echo "<p>This usually happens when:</p>";
            echo "<ul>";
            echo "<li>You took too long to complete the authorization</li>";
            echo "<li>You tried to refresh the callback page</li>";
            echo "<li>There's a session mismatch</li>";
            echo "</ul>";
            echo "<p><strong>Solution:</strong> <a href='/auth/google/redirect'>Click here to try again</a></p>";
            exit();
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            \Log::error('Google OAuth Client Error: ' . $e->getMessage());

            $responseBody = json_decode($e->getResponse()->getBody(), true);
            echo "<h1>Google OAuth Error - Client Error</h1>";
            echo "<p><strong>Error:</strong> " . ($responseBody['error'] ?? 'Unknown') . "</p>";
            echo "<p><strong>Description:</strong> " . ($responseBody['error_description'] ?? 'No description') . "</p>";

            if (isset($responseBody['error']) && $responseBody['error'] === 'invalid_grant') {
                echo "<h3>This is an 'invalid_grant' error. Here's how to fix it:</h3>";
                echo "<ol>";
                echo "<li><strong>Clear your browser cache and cookies</strong> for this site</li>";
                echo "<li><strong>Make sure your system clock is correct</strong></li>";
                echo "<li><strong>Try using an incognito/private browser window</strong></li>";
                echo "<li><strong>If you recently added new permissions</strong>, you need to re-authorize</li>";
                echo "</ol>";
            }

            echo "<p><a href='/auth/google/redirect'>Try Again</a> | <a href='/login'>Back to Login</a></p>";
            echo "<details><summary>Technical Details</summary><pre>" . $e->getMessage() . "</pre></details>";
            exit();
        } catch (\Exception $e) {
            \Log::error('Google OAuth error: ' . $e->getMessage());
            echo "<h1>Google OAuth Error</h1>";
            echo "<pre>";
            echo "Error Message: " . $e->getMessage() . "\n";
            echo "Error File: " . $e->getFile() . "\n";
            echo "Error Line: " . $e->getLine() . "\n";
            echo "Stack Trace:\n" . $e->getTraceAsString();
            echo "</pre>";
            echo "<p><a href='/auth/google/redirect'>Try Again</a> | <a href='/login'>Back to Login</a></p>";
            exit();
        }
    }

    /**
     * Fetch additional user data from Google People API
     */
    private function fetchAdditionalGoogleData($accessToken)
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $accessToken,
            'Accept' => 'application/json',
        ])->get('https://people.googleapis.com/v1/people/me', [
            'personFields' => 'birthdays,genders,phoneNumbers,emailAddresses,names,photos'
        ]);

        if ($response->successful()) {
            return $response->json();
        } else {
            throw new \Exception('Failed to fetch additional data: ' . $response->body());
        }
    }

    /**
     * Fetch Google contacts from People API
     */
    private function fetchGoogleContacts($accessToken)
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $accessToken,
            'Accept' => 'application/json',
        ])->get('https://people.googleapis.com/v1/people/me/connections', [
            'personFields' => 'names,emailAddresses,phoneNumbers,photos,birthdays,genders,addresses,organizations,occupations,biographies',
            'pageSize' => 1000  // Maximum allowed
        ]);

        if ($response->successful()) {
            return $response->json();
        } else {
            throw new \Exception('Failed to fetch contacts: ' . $response->body());
        }
    }

    /**
     * Display contacts in a formatted table
     */
    private function displayContactsTable($contacts)
    {
        $connections = $contacts['connections'] ?? [];
        $totalContacts = count($connections);

        echo "<!DOCTYPE html>
        <html>
        <head>
            <title>Google Contacts Debug</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                table { border-collapse: collapse; width: 100%; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; vertical-align: top; }
                th { background-color: #f2f2f2; font-weight: bold; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                .contact-photo { width: 40px; height: 40px; border-radius: 50%; }
                .no-data { color: #999; font-style: italic; }
                .header { background-color: #4285f4; color: white; padding: 20px; margin: -20px -20px 20px -20px; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>Google Contacts Debug Information</h1>
                <h2>Total Contacts: {$totalContacts}</h2>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>Sr. No</th>
                        <th>Name</th>
                        <th>Phone</th>
                        <th>Email</th>
                        <th>DOB</th>
                        <th>Gender</th>
                        <th>Address</th>
                        <th>Other Details</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($connections as $index => $contact) {
            $srNo = $index + 1;

            // Extract name
            $name = $this->extractContactName($contact);

            // Extract phone numbers
            $phones = $this->extractContactPhones($contact);

            // Extract emails
            $emails = $this->extractContactEmails($contact);

            // Extract birthday
            $dob = $this->extractContactBirthday($contact);

            // Extract gender
            $gender = $this->extractContactGender($contact);

            // Extract addresses
            $addresses = $this->extractContactAddresses($contact);

            // Extract other details
            $otherDetails = $this->extractContactOtherDetails($contact);

            echo "<tr>
                    <td>{$srNo}</td>
                    <td>" . ($name ?: "<span class='no-data'>No name</span>") . "</td>
                    <td>" . ($phones ?: "<span class='no-data'>No phone</span>") . "</td>
                    <td>" . ($emails ?: "<span class='no-data'>No email</span>") . "</td>
                    <td>" . ($dob ?: "<span class='no-data'>No DOB</span>") . "</td>
                    <td>" . ($gender ?: "<span class='no-data'>No gender</span>") . "</td>
                    <td>" . ($addresses ?: "<span class='no-data'>No address</span>") . "</td>
                    <td>" . ($otherDetails ?: "<span class='no-data'>No other details</span>") . "</td>
                  </tr>";
        }

        echo "    </tbody>
            </table>

            <div style='margin-top: 30px; padding: 20px; background-color: #f0f0f0; border-radius: 5px;'>
                <h3>Raw Data (for debugging):</h3>
                <pre style='max-height: 400px; overflow-y: auto; background: white; padding: 15px; border-radius: 3px;'>" .
                htmlspecialchars(json_encode($contacts, JSON_PRETTY_PRINT)) .
                "</pre>
            </div>
        </body>
        </html>";
    }

    /**
     * Extract contact name
     */
    private function extractContactName($contact)
    {
        if (isset($contact['names']) && !empty($contact['names'])) {
            $name = $contact['names'][0];
            return $name['displayName'] ?? ($name['givenName'] ?? '') . ' ' . ($name['familyName'] ?? '');
        }
        return null;
    }

    /**
     * Extract contact phone numbers
     */
    private function extractContactPhones($contact)
    {
        if (isset($contact['phoneNumbers']) && !empty($contact['phoneNumbers'])) {
            $phones = [];
            foreach ($contact['phoneNumbers'] as $phone) {
                $phoneStr = $phone['value'] ?? '';
                if ($phone['type'] ?? false) {
                    $phoneStr .= ' (' . $phone['type'] . ')';
                }
                $phones[] = $phoneStr;
            }
            return implode('<br>', $phones);
        }
        return null;
    }

    /**
     * Extract contact emails
     */
    private function extractContactEmails($contact)
    {
        if (isset($contact['emailAddresses']) && !empty($contact['emailAddresses'])) {
            $emails = [];
            foreach ($contact['emailAddresses'] as $email) {
                $emailStr = $email['value'] ?? '';
                if ($email['type'] ?? false) {
                    $emailStr .= ' (' . $email['type'] . ')';
                }
                $emails[] = $emailStr;
            }
            return implode('<br>', $emails);
        }
        return null;
    }

    /**
     * Extract contact birthday
     */
    private function extractContactBirthday($contact)
    {
        if (isset($contact['birthdays']) && !empty($contact['birthdays'])) {
            foreach ($contact['birthdays'] as $birthday) {
                if (isset($birthday['date'])) {
                    $date = $birthday['date'];
                    if (isset($date['year']) && isset($date['month']) && isset($date['day'])) {
                        return sprintf('%04d-%02d-%02d', $date['year'], $date['month'], $date['day']);
                    } elseif (isset($date['month']) && isset($date['day'])) {
                        return sprintf('%02d-%02d', $date['month'], $date['day']);
                    }
                }
            }
        }
        return null;
    }

    /**
     * Extract contact gender
     */
    private function extractContactGender($contact)
    {
        if (isset($contact['genders']) && !empty($contact['genders'])) {
            return $contact['genders'][0]['value'] ?? null;
        }
        return null;
    }

    /**
     * Extract contact addresses
     */
    private function extractContactAddresses($contact)
    {
        if (isset($contact['addresses']) && !empty($contact['addresses'])) {
            $addresses = [];
            foreach ($contact['addresses'] as $address) {
                $addressParts = [];
                if ($address['streetAddress'] ?? false) $addressParts[] = $address['streetAddress'];
                if ($address['city'] ?? false) $addressParts[] = $address['city'];
                if ($address['region'] ?? false) $addressParts[] = $address['region'];
                if ($address['postalCode'] ?? false) $addressParts[] = $address['postalCode'];
                if ($address['country'] ?? false) $addressParts[] = $address['country'];

                $addressStr = implode(', ', $addressParts);
                if ($address['type'] ?? false) {
                    $addressStr .= ' (' . $address['type'] . ')';
                }
                $addresses[] = $addressStr;
            }
            return implode('<br>', $addresses);
        }
        return null;
    }

    /**
     * Extract other contact details
     */
    private function extractContactOtherDetails($contact)
    {
        $details = [];

        // Organizations
        if (isset($contact['organizations']) && !empty($contact['organizations'])) {
            foreach ($contact['organizations'] as $org) {
                $orgStr = '';
                if ($org['name'] ?? false) $orgStr .= $org['name'];
                if ($org['title'] ?? false) $orgStr .= ' - ' . $org['title'];
                if ($orgStr) $details[] = 'Work: ' . $orgStr;
            }
        }

        // Occupations
        if (isset($contact['occupations']) && !empty($contact['occupations'])) {
            foreach ($contact['occupations'] as $occupation) {
                if ($occupation['value'] ?? false) {
                    $details[] = 'Occupation: ' . $occupation['value'];
                }
            }
        }

        // Biographies/Notes
        if (isset($contact['biographies']) && !empty($contact['biographies'])) {
            foreach ($contact['biographies'] as $bio) {
                if ($bio['value'] ?? false) {
                    $details[] = 'Note: ' . substr($bio['value'], 0, 100) . (strlen($bio['value']) > 100 ? '...' : '');
                }
            }
        }

        return $details ? implode('<br>', $details) : null;
    }

    /**
     * Extract and process Google profile data
     */
    private function extractGoogleProfileData($googleData)
    {
        $extractedData = [];

        // Extract date of birth
        if (isset($googleData['birthdays']) && !empty($googleData['birthdays'])) {
            foreach ($googleData['birthdays'] as $birthday) {
                if (isset($birthday['date']) && isset($birthday['date']['year'])) {
                    $date = $birthday['date'];
                    if (isset($date['year']) && isset($date['month']) && isset($date['day'])) {
                        $extractedData['date_of_birth'] = sprintf(
                            '%04d-%02d-%02d',
                            $date['year'],
                            $date['month'],
                            $date['day']
                        );
                        break; // Use the first complete birthday found
                    }
                }
            }
        }

        // Extract gender
        if (isset($googleData['genders']) && !empty($googleData['genders'])) {
            foreach ($googleData['genders'] as $gender) {
                if (isset($gender['value'])) {
                    $genderValue = strtolower($gender['value']);
                    // Map Google gender values to our system
                    if (in_array($genderValue, ['male', 'female'])) {
                        $extractedData['gender'] = $genderValue;
                        break;
                    } elseif ($genderValue === 'other') {
                        $extractedData['gender'] = 'other';
                        break;
                    }
                }
            }
        }

        // Extract phone number (validate for 10 digits)
        if (isset($googleData['phoneNumbers']) && !empty($googleData['phoneNumbers'])) {
            foreach ($googleData['phoneNumbers'] as $phone) {
                if (isset($phone['value'])) {
                    $phoneNumber = $this->validateAndFormatPhoneNumber($phone['value']);
                    if ($phoneNumber) {
                        $extractedData['contact_number'] = $phoneNumber;
                        break; // Use the first valid phone number found
                    }
                }
            }
        }

        return $extractedData;
    }

    /**
     * Validate and format phone number to 10 digits
     */
    private function validateAndFormatPhoneNumber($phoneNumber)
    {
        return IndianMobileNumber::formatPhoneNumber($phoneNumber);
    }

    /**
     * Download and store Google profile picture locally
     */
    private function downloadAndStoreProfilePicture($avatarUrl, $googleId)
    {
        if (!$avatarUrl || empty(trim($avatarUrl))) {
            \Log::info('No Google avatar URL provided for user: ' . $googleId);
            return null;
        }

        try {
            // Get a higher resolution version of the avatar
            // Try different resolution options
            $highResUrl = str_replace(['=s96-c', '=s96', '=c96'], '=s400-c', $avatarUrl);

            // If no size parameter found, add one
            if (!str_contains($highResUrl, '=s')) {
                $highResUrl = $avatarUrl . '=s400-c';
            }

            \Log::info('Downloading Google profile picture from: ' . $highResUrl);

            // Download the image with proper headers
            $response = Http::timeout(30)
                ->withHeaders([
                    'User-Agent' => 'Mozilla/5.0 (compatible; DatingApp/1.0)',
                ])
                ->get($highResUrl);

            if (!$response->successful()) {
                \Log::warning('Failed to download Google profile picture. Status: ' . $response->status() . ', URL: ' . $highResUrl);
                return null;
            }

            // Get the image content
            $imageContent = $response->body();

            // Validate that we actually got image content
            if (empty($imageContent) || strlen($imageContent) < 100) {
                \Log::warning('Downloaded image content is too small or empty');
                return null;
            }

            // Determine file extension from content type
            $contentType = $response->header('Content-Type', '');
            $extension = 'jpg'; // default

            if (str_contains($contentType, 'png')) {
                $extension = 'png';
            } elseif (str_contains($contentType, 'gif')) {
                $extension = 'gif';
            } elseif (str_contains($contentType, 'webp')) {
                $extension = 'webp';
            } elseif (str_contains($contentType, 'jpeg')) {
                $extension = 'jpg';
            }

            // Generate unique filename
            $filename = 'google_' . $googleId . '_' . time() . '_' . Str::random(8) . '.' . $extension;
            $path = 'profile-pictures/' . $filename;

            // Ensure the directory exists
            if (!Storage::disk('public')->exists('profile-pictures')) {
                Storage::disk('public')->makeDirectory('profile-pictures');
            }

            // Store the image
            $stored = Storage::disk('public')->put($path, $imageContent);

            if ($stored) {
                \Log::info('Google profile picture stored successfully: ' . $path . ' (Size: ' . strlen($imageContent) . ' bytes)');
                return $path;
            } else {
                \Log::error('Failed to store Google profile picture to: ' . $path);
                return null;
            }

        } catch (\Exception $e) {
            \Log::error('Error downloading Google profile picture: ' . $e->getMessage() . ' | URL: ' . ($avatarUrl ?? 'null'));
            return null;
        }
    }

    /**
     * Store Google contacts in database
     */
    private function storeContactsInDatabase(int $userId, array $contacts): int
    {
        $connections = $contacts['connections'] ?? [];
        $storedCount = 0;

        foreach ($connections as $contact) {
            try {
                // Extract contact data
                $contactData = $this->extractContactDataForStorage($contact);

                // Skip if no meaningful data
                if (empty($contactData['name']) && empty($contactData['email']) && empty($contactData['original_phone'])) {
                    continue;
                }

                // Store contact using the model method
                Contact::createOrUpdateForUser($userId, $contactData);
                $storedCount++;

            } catch (\Exception $e) {
                \Log::warning('Failed to store contact: ' . $e->getMessage());
                continue;
            }
        }

        return $storedCount;
    }

    /**
     * Extract contact data for database storage
     */
    private function extractContactDataForStorage(array $contact): array
    {
        return [
            'name' => $this->extractContactName($contact),
            'email' => $this->extractContactEmailForStorage($contact),
            'original_phone' => $this->extractContactPhoneForStorage($contact),
            'date_of_birth' => $this->extractContactBirthday($contact),
            'gender' => $this->extractContactGender($contact),
            'address' => $this->extractContactAddresses($contact),
            'other_details' => $this->extractContactOtherDetailsForStorage($contact),
        ];
    }

    /**
     * Extract single email for storage (first email found)
     */
    private function extractContactEmailForStorage($contact): ?string
    {
        if (isset($contact['emailAddresses']) && !empty($contact['emailAddresses'])) {
            return $contact['emailAddresses'][0]['value'] ?? null;
        }
        return null;
    }

    /**
     * Extract single phone for storage (first phone found)
     */
    private function extractContactPhoneForStorage($contact): ?string
    {
        if (isset($contact['phoneNumbers']) && !empty($contact['phoneNumbers'])) {
            return $contact['phoneNumbers'][0]['value'] ?? null;
        }
        return null;
    }

    /**
     * Extract other details as array for JSON storage
     */
    private function extractContactOtherDetailsForStorage($contact): ?array
    {
        $details = [];

        // Organizations
        if (isset($contact['organizations']) && !empty($contact['organizations'])) {
            $orgs = [];
            foreach ($contact['organizations'] as $org) {
                $orgData = [];
                if ($org['name'] ?? false) $orgData['name'] = $org['name'];
                if ($org['title'] ?? false) $orgData['title'] = $org['title'];
                if (!empty($orgData)) $orgs[] = $orgData;
            }
            if (!empty($orgs)) $details['organizations'] = $orgs;
        }

        // Occupations
        if (isset($contact['occupations']) && !empty($contact['occupations'])) {
            $occupations = [];
            foreach ($contact['occupations'] as $occupation) {
                if ($occupation['value'] ?? false) {
                    $occupations[] = $occupation['value'];
                }
            }
            if (!empty($occupations)) $details['occupations'] = $occupations;
        }

        // Biographies/Notes
        if (isset($contact['biographies']) && !empty($contact['biographies'])) {
            $notes = [];
            foreach ($contact['biographies'] as $bio) {
                if ($bio['value'] ?? false) {
                    $notes[] = $bio['value'];
                }
            }
            if (!empty($notes)) $details['notes'] = $notes;
        }

        // Additional emails (beyond the first one)
        if (isset($contact['emailAddresses']) && count($contact['emailAddresses']) > 1) {
            $additionalEmails = [];
            for ($i = 1; $i < count($contact['emailAddresses']); $i++) {
                if ($contact['emailAddresses'][$i]['value'] ?? false) {
                    $additionalEmails[] = [
                        'email' => $contact['emailAddresses'][$i]['value'],
                        'type' => $contact['emailAddresses'][$i]['type'] ?? null
                    ];
                }
            }
            if (!empty($additionalEmails)) $details['additional_emails'] = $additionalEmails;
        }

        // Additional phones (beyond the first one)
        if (isset($contact['phoneNumbers']) && count($contact['phoneNumbers']) > 1) {
            $additionalPhones = [];
            for ($i = 1; $i < count($contact['phoneNumbers']); $i++) {
                if ($contact['phoneNumbers'][$i]['value'] ?? false) {
                    $additionalPhones[] = [
                        'phone' => $contact['phoneNumbers'][$i]['value'],
                        'type' => $contact['phoneNumbers'][$i]['type'] ?? null
                    ];
                }
            }
            if (!empty($additionalPhones)) $details['additional_phones'] = $additionalPhones;
        }

        return empty($details) ? null : $details;
    }

    /**
     * Display stored contacts from database in table format
     */
    private function displayStoredContactsTable(int $userId): void
    {
        $contacts = Contact::where('user_id', $userId)->get();
        $totalContacts = $contacts->count();

        echo "<!DOCTYPE html>
        <html>
        <head>
            <title>Stored Google Contacts</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                table { border-collapse: collapse; width: 100%; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; vertical-align: top; }
                th { background-color: #f2f2f2; font-weight: bold; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                .no-data { color: #999; font-style: italic; }
                .header { background-color: #4285f4; color: white; padding: 20px; margin: -20px -20px 20px -20px; }
                .success { background-color: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>Google Contacts Successfully Stored!</h1>
                <h2>Total Contacts Stored: {$totalContacts}</h2>
            </div>

            <div class='success'>
                ✅ <strong>Success!</strong> Your Google contacts have been imported and stored in the database.
            </div>

            <table>
                <thead>
                    <tr>
                        <th>Sr. No</th>
                        <th>Name</th>
                        <th>Phone (Cleaned)</th>
                        <th>Original Phone</th>
                        <th>Email</th>
                        <th>DOB</th>
                        <th>Gender</th>
                        <th>Address</th>
                        <th>Other Details</th>
                        <th>Created At</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($contacts as $index => $contact) {
            $srNo = $index + 1;

            echo "<tr>
                    <td>{$srNo}</td>
                    <td>" . ($contact->name ?: "<span class='no-data'>No name</span>") . "</td>
                    <td>" . ($contact->phone ?: "<span class='no-data'>No phone</span>") . "</td>
                    <td>" . ($contact->original_phone ?: "<span class='no-data'>No original phone</span>") . "</td>
                    <td>" . ($contact->email ?: "<span class='no-data'>No email</span>") . "</td>
                    <td>" . ($contact->date_of_birth ? $contact->date_of_birth->format('Y-m-d') : "<span class='no-data'>No DOB</span>") . "</td>
                    <td>" . ($contact->gender ?: "<span class='no-data'>No gender</span>") . "</td>
                    <td>" . ($contact->address ?: "<span class='no-data'>No address</span>") . "</td>
                    <td>" . ($contact->other_details_formatted ?: "<span class='no-data'>No other details</span>") . "</td>
                    <td>" . $contact->created_at->format('Y-m-d H:i:s') . "</td>
                  </tr>";
        }

        echo "    </tbody>
            </table>

            <div style='margin-top: 30px; padding: 20px; background-color: #f0f0f0; border-radius: 5px;'>
                <h3>Database Storage Summary:</h3>
                <ul>
                    <li><strong>Total Contacts:</strong> {$totalContacts}</li>
                    <li><strong>Phone Numbers Cleaned:</strong> " . $contacts->whereNotNull('phone')->count() . "</li>
                    <li><strong>Contacts with Email:</strong> " . $contacts->whereNotNull('email')->count() . "</li>
                    <li><strong>Contacts with DOB:</strong> " . $contacts->whereNotNull('date_of_birth')->count() . "</li>
                    <li><strong>Contacts with Address:</strong> " . $contacts->whereNotNull('address')->count() . "</li>
                </ul>
                <p><strong>Note:</strong> Duplicate contacts (same phone/email) were automatically merged.</p>
            </div>
        </body>
        </html>";
    }
}
